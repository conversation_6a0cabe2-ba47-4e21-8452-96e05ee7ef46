import json
import os
import logging

logger = logging.getLogger(__name__)

# Config chung cho game - không thay đổi theo account
GAME_CONFIG = {
    "package_name": "com.growx.kht1",
    "activity_name": "com.jxclient.release.jxclient",
    "adb_path": r"C:\LDPlayer\LDPlayer9\adb.exe",
    "ldconsole_path": r"C:\LDPlayer\LDPlayer9\ldconsole.exe",
    "loop_delay": 5,
    "log_level": "INFO",
    "save_log": True,

    # Tọa độ UI game
    "username_x": 506,
    "username_y": 232,
    "password_x": 522,
    "password_y": 309,
    "minimap_pos": (942, 54),
    "xa_phu_pos": (137, 200),

    "maps": {
        "tuong_duong": {
            "xa_phu": (188, 196),
            "thu_kho": (195, 201)
        },
        "bien_kinh": {
            "xa_phu": (232, 182),
            "thu_kho": (231, 184)
        }
    },

    # Danh sách khu vực train có sẵn
    "train_areas": {
        "khu_vuc_1": {
            "name": "Khu vực luyện công 50",
            "maps": {
                "map_1": {"name": "Địa đạo hậu viện", "x": 100, "y": 200},
                "map_2": {"name": "Hưởng Thủy động", "x": 150, "y": 250}
            }
        },
        "khu_vuc_3": {
            "name": "Khu vực luyện công 70",
            "maps": {
                "map_1": {"name": "Đại Tù động", "x": 200, "y": 300},
                "map_2": {"name": "Lâm Du quan", "x": 204, "y": 224}
            }
        }
    }
}

# Config mặc định cho account - có thể thay đổi cho từng account
DEFAULT_ACCOUNT_CONFIG = {
    "auto_login": False,
    "login_retry": 3,
    "auto_train": False,
    "loop": False,
    # "auto_daily": False,
    # "auto_event": False,
    # "auto_dungeon": False,

    # Cài đặt train mặc định
    "train_area": "khu_vuc_1",
    "train_map": "map_1",
    "train_x": 199,
    "train_y": 199,
    # "auto_fight_enabled": True,
}

# Để tương thích ngược, merge cả 2 config
DEFAULT_CONFIG = {**GAME_CONFIG, **DEFAULT_ACCOUNT_CONFIG}


def load_game_config(config_file: str = "game_settings.json") -> dict:
    """
    Load game configuration from file
    """
    config = GAME_CONFIG.copy()

    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                config.update(user_config)
        except Exception as e:
            logger.error(f"Error loading game config: {str(e)}")

    return config


def save_game_config(config: dict, config_file: str = "game_settings.json"):
    """
    Save game configuration to file
    """
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        logger.info(f"Game config saved to {config_file}")
    except Exception as e:
        logger.error(f"Error saving game config: {str(e)}")


def load_default_account_config(config_file: str = "default_account_settings.json") -> dict:
    """
    Load default account configuration from file
    """
    config = DEFAULT_ACCOUNT_CONFIG.copy()

    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                config.update(user_config)
        except Exception as e:
            logger.error(f"Error loading default account config: {str(e)}")

    return config


def save_default_account_config(config: dict, config_file: str = "default_account_settings.json"):
    """
    Save default account configuration to file
    """
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        logger.info(f"Default account config saved to {config_file}")
    except Exception as e:
        logger.error(f"Error saving default account config: {str(e)}")


def load_config(config_file: str = "settings.json") -> dict:
    """
    Load configuration from file (for backward compatibility)
    """
    config = DEFAULT_CONFIG.copy()

    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                config.update(user_config)
        except Exception as e:
            logger.error(f"Error loading config: {str(e)}")

    return config


def save_config(config: dict, config_file: str = "settings.json"):
    """
    Save configuration to file (for backward compatibility)
    """
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        logger.info(f"Config saved to {config_file}")
    except Exception as e:
        logger.error(f"Error saving config: {str(e)}")


def get_merged_config() -> dict:
    """
    Get merged configuration from both game and default account configs
    """
    game_config = load_game_config()
    account_config = load_default_account_config()
    return {**game_config, **account_config}