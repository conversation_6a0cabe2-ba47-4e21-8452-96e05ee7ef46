import cv2
import numpy as np
import time
from typing import Optional, Tuple
import os
import logging

logger = logging.getLogger(__name__)


def find_image(screenshot: np.ndarray, template_path: str, gray_scale: bool = True, 
               threshold: float = 0.8) -> Optional[Tuple[int, int]]:
    """
    Find template image in screenshot
    Returns center coordinates of found image or None
    """
    if not os.path.exists(template_path):
        logger.error(f"Template not found: {template_path}")
        return None
    
    template = cv2.imread(template_path)

    if template is None:
        logger.error(f"Failed to load template: {template_path}")
        return None
    
    if gray_scale:
        # Convert sang grayscale để tăng tốc độ
        screenshot = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
        template = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
    result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)

    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    
    if max_val >= threshold:
        template_height, template_width = template.shape[:2]
        center_x = max_loc[0] + template_width // 2
        center_y = max_loc[1] + template_height // 2
        return (center_x, center_y)
    
    return None


def find_all_images(screenshot: np.ndarray, template_path: str,
                    threshold: float = 0.8) -> list:
    """
    Find all occurrences of template image in screenshot
    Returns list of center coordinates
    """
    if not os.path.exists(template_path):
        logger.error(f"Template not found: {template_path}")
        return []
    
    template = cv2.imread(template_path)
    if template is None:
        logger.error(f"Failed to load template: {template_path}")
        return []
    
    result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
    locations = np.where(result >= threshold)
    
    template_height, template_width = template.shape[:2]
    positions = []
    
    for pt in zip(*locations[::-1]):
        center_x = pt[0] + template_width // 2
        center_y = pt[1] + template_height // 2
        positions.append((center_x, center_y))
    
    return positions


def wait_for_image(adb, device_id: str, template_path: str, gray_scale: bool = True,
                   timeout: int = 30, threshold: float = 0.8) -> bool:
    """
    Wait for image to appear on screen
    """
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        screenshot = adb.screenshot(device_id)
        if screenshot is not None:
            if find_image(screenshot, template_path, gray_scale, threshold):
                return True
        time.sleep(2)
    
    return False

def wait_for_image_disappear(adb, device_id: str, template_path: str,
                             timeout: int = 30, threshold: float = 0.8) -> bool:
    """
    Wait for image to disappear from screen
    """
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        screenshot = adb.screenshot(device_id)
        if screenshot is not None:
            if not find_image(screenshot, template_path, threshold):
                return True
        time.sleep(2)
    
    return False


def compare_images(img1: np.ndarray, img2: np.ndarray) -> float:
    """
    Compare two images and return similarity score
    """
    if img1.shape != img2.shape:
        return 0.0
    
    # Convert to grayscale
    gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
    gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
    
    # Calculate structural similarity
    score = cv2.matchTemplate(gray1, gray2, cv2.TM_CCOEFF_NORMED)[0][0]
    return float(score)


def crop_image(image: np.ndarray, x: int, y: int, 
               width: int, height: int) -> np.ndarray:
    """
    Crop a region from image
    """
    return image[y:y+height, x:x+width]


def save_screenshot(image: np.ndarray, filename: str, folder: str = "screenshots"):
    """
    Save screenshot to file
    """
    if not os.path.exists(folder):
        os.makedirs(folder)
    
    filepath = os.path.join(folder, filename)
    cv2.imwrite(filepath, image)
    logger.info(f"Screenshot saved: {filepath}")
    return filepath