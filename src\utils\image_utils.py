import cv2
import numpy as np
import time
from typing import Optional, Tuple, Dict
import os
import logging
import gc
import weakref

logger = logging.getLogger(__name__)

# Cache cho template images để tránh load lại nhiều lần
_template_cache: Dict[str, np.ndarray] = {}
_template_cache_refs: Dict[str, weakref.ref] = {}


def _load_template_cached(template_path: str) -> Optional[np.ndarray]:
    """
    Load template with caching để tránh load lại nhiều lần
    """
    if not os.path.exists(template_path):
        logger.error(f"Template not found: {template_path}")
        return None

    # Kiểm tra cache trước
    if template_path in _template_cache:
        return _template_cache[template_path]

    # Load template mới
    template = cv2.imread(template_path)
    if template is None:
        logger.error(f"Failed to load template: {template_path}")
        return None

    # Cache template (giới hạn cache size để tránh memory leak)
    if len(_template_cache) > 100:  # Giới hạn 100 templates
        # Xóa template cũ nhất
        oldest_key = next(iter(_template_cache))
        del _template_cache[oldest_key]
        _template_cache_refs.pop(oldest_key, None)

    _template_cache[template_path] = template
    return template


def find_image(screenshot: np.ndarray, template_path: str, gray_scale: bool = True,
               threshold: float = 0.8) -> Optional[Tuple[int, int]]:
    """
    Find template image in screenshot với memory optimization
    Returns center coordinates of found image or None
    """
    template = _load_template_cached(template_path)
    if template is None:
        return None

    try:
        # Tạo copy để tránh modify original arrays
        if gray_scale:
            # Convert sang grayscale để tăng tốc độ
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)

            # Giải phóng bộ nhớ ngay lập tức
            del screenshot_gray, template_gray
        else:
            result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)

        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

        # Giải phóng result array
        del result

        if max_val >= threshold:
            template_height, template_width = template.shape[:2]
            center_x = max_loc[0] + template_width // 2
            center_y = max_loc[1] + template_height // 2
            return (center_x, center_y)

        return None

    except Exception as e:
        logger.error(f"Error in find_image: {str(e)}")
        return None
    finally:
        # Force garbage collection để giải phóng bộ nhớ
        gc.collect()


def find_all_images(screenshot: np.ndarray, template_path: str,
                    threshold: float = 0.8) -> list:
    """
    Find all occurrences of template image in screenshot
    Returns list of center coordinates
    """
    if not os.path.exists(template_path):
        logger.error(f"Template not found: {template_path}")
        return []
    
    template = cv2.imread(template_path)
    if template is None:
        logger.error(f"Failed to load template: {template_path}")
        return []
    
    result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
    locations = np.where(result >= threshold)
    
    template_height, template_width = template.shape[:2]
    positions = []
    
    for pt in zip(*locations[::-1]):
        center_x = pt[0] + template_width // 2
        center_y = pt[1] + template_height // 2
        positions.append((center_x, center_y))
    
    return positions


def wait_for_image(adb, device_id: str, template_path: str, gray_scale: bool = True,
                   timeout: int = 30, threshold: float = 0.8) -> bool:
    """
    Wait for image to appear on screen với memory optimization
    """
    start_time = time.time()

    while time.time() - start_time < timeout:
        screenshot = adb.screenshot(device_id)
        if screenshot is not None:
            try:
                found = find_image(screenshot, template_path, gray_scale, threshold)
                # Giải phóng screenshot ngay sau khi sử dụng
                del screenshot

                if found:
                    return True
            except Exception as e:
                logger.error(f"Error in wait_for_image: {str(e)}")
                # Giải phóng screenshot trong trường hợp lỗi
                if 'screenshot' in locals():
                    del screenshot

        # Force garbage collection mỗi vài lần để tránh tích lũy
        if int(time.time() - start_time) % 10 == 0:
            gc.collect()

        time.sleep(2)

    return False

def wait_for_image_disappear(adb, device_id: str, template_path: str,
                             timeout: int = 30, threshold: float = 0.8) -> bool:
    """
    Wait for image to disappear from screen với memory optimization
    """
    start_time = time.time()

    while time.time() - start_time < timeout:
        screenshot = adb.screenshot(device_id)
        if screenshot is not None:
            try:
                found = find_image(screenshot, template_path, True, threshold)
                # Giải phóng screenshot ngay sau khi sử dụng
                del screenshot

                if not found:
                    return True
            except Exception as e:
                logger.error(f"Error in wait_for_image_disappear: {str(e)}")
                # Giải phóng screenshot trong trường hợp lỗi
                if 'screenshot' in locals():
                    del screenshot

        # Force garbage collection mỗi vài lần để tránh tích lũy
        if int(time.time() - start_time) % 10 == 0:
            gc.collect()

        time.sleep(2)

    return False


def clear_template_cache():
    """
    Xóa cache template để giải phóng bộ nhớ
    """
    global _template_cache, _template_cache_refs
    _template_cache.clear()
    _template_cache_refs.clear()
    gc.collect()
    logger.info("Template cache cleared")


def compare_images(img1: np.ndarray, img2: np.ndarray) -> float:
    """
    Compare two images and return similarity score
    """
    if img1.shape != img2.shape:
        return 0.0
    
    # Convert to grayscale
    gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
    gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
    
    # Calculate structural similarity
    score = cv2.matchTemplate(gray1, gray2, cv2.TM_CCOEFF_NORMED)[0][0]
    return float(score)


def crop_image(image: np.ndarray, x: int, y: int, 
               width: int, height: int) -> np.ndarray:
    """
    Crop a region from image
    """
    return image[y:y+height, x:x+width]


def save_screenshot(image: np.ndarray, filename: str, folder: str = "screenshots"):
    """
    Save screenshot to file
    """
    if not os.path.exists(folder):
        os.makedirs(folder)
    
    filepath = os.path.join(folder, filename)
    cv2.imwrite(filepath, image)
    logger.info(f"Screenshot saved: {filepath}")
    return filepath