import subprocess
import re
import time
from typing import List, Optional, Tuple
import logging
from PIL import Image
import numpy as np
import cv2
import io
from ..config.config import load_config
import os
import threading

logger = logging.getLogger(__name__)


class ADBController:
    def __init__(self):
        config = load_config()
        self.adb_path = config.get("adb_path", r"C:\LDPlayer\LDPlayer9\adb.exe")
        self.ldconsole_path = config.get("ldconsole_path", r"C:\LDPlayer\LDPlayer9\ldconsole.exe")
        self.devices = []
        self.device_names = {}  # Mapping device_id -> display_name
        self.connected_devices = set()  # Track connected devices
        self.active_devices = set()  # Track devices with running accounts
        self.screenshot_lock = threading.Lock()  # Lock để đảm bảo thread-safe
        self._cleanup_temp_files()  # Dọn dẹp file tạm thời cũ

    def _cleanup_temp_files(self):
        """Dọn dẹp các file screenshot tạm thời cũ"""
        temp_dir = "temp_screenshots"
        if os.path.exists(temp_dir):
            try:
                for filename in os.listdir(temp_dir):
                    if filename.startswith("screen_temp_") and filename.endswith(".png"):
                        file_path = os.path.join(temp_dir, filename)
                        os.remove(file_path)
                        logger.debug(f"Cleaned up temp file: {file_path}")
            except Exception as e:
                logger.warning(f"Error cleaning temp files: {str(e)}")

    def execute_command(self, command: str, device_id: Optional[str] = None) -> str:
        cmd = [self.adb_path]
        if device_id:
            cmd.extend(["-s", device_id])
        cmd.extend(command.split())
        
        try:
            # logger.info(f"Executing ADB command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                logger.error(f"ADB command failed: {result.stderr}")
                return ""
            return result.stdout
        except subprocess.TimeoutExpired:
            logger.error(f"ADB command timeout: {' '.join(cmd)}")
            return ""
        except Exception as e:
            logger.error(f"ADB command error: {str(e)}")
            return ""

    def get_ldplayer_instances(self) -> dict:
        """
        Get LDPlayer instances with their names and ports
        Returns: dict mapping device_id -> display_name
        """
        instances = {}
        try:
            # Get list of LDPlayer instances
            cmd = [self.ldconsole_path, "list2"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.strip():
                        # Format: index,name,top_window_handle,bind_window_handle,is_running,pid,vbox_pid
                        parts = line.split(',')
                        if len(parts) >= 0:
                            if parts[0].strip() == "99999":
                                continue
                        if len(parts) >= 5:
                            index = parts[0].strip()
                            name = parts[1].strip()
                            is_running = parts[4].strip()
                            if is_running == "1":  # Only running instances
                                # Calculate port: LDPlayer uses index * 2 + 5555
                                port = int(index) * 2 + 5555
                                device_id = f"127.0.0.1:{port}"
                                instances[device_id] = name
                                logger.debug(f"Found LDPlayer instance: {name} -> {device_id}")

        except Exception as e:
            logger.warning(f"Could not get LDPlayer instances: {str(e)}")

        return instances

    def refresh_devices(self) -> List[str]:
        """
        Check connection on each port to get list of devices với optimization
        """
        devices = []

        # Cleanup existing connections trước khi scan
        self._cleanup_all_connections()

        # First, try to get LDPlayer instances for better mapping
        ldplayer_instances = self.get_ldplayer_instances()
        # If we have LDPlayer instances, prioritize those
        if ldplayer_instances:
            logger.info("Using LDPlayer instance detection")
            for device_id, name in ldplayer_instances.items():
                if self._test_device_connection(device_id, name):
                    devices.append(device_id)
                    self.device_names[device_id] = name
                    self.connected_devices.add(device_id)
                    logger.info(f"Found LDPlayer: {name} -> {device_id}")
        else:
            # Fallback to port scanning - chỉ scan ports cần thiết
            logger.info("Fallback to port scanning")
            # Giới hạn scan range để tránh waste time
            port_range = range(5555, 5575, 2)  # Giảm từ 5595 xuống 5575

            for port in port_range:
                device_id = f"127.0.0.1:{port}"
                if self._test_device_connection(device_id):
                    devices.append(device_id)
                    # Try to guess name from port
                    index = (port - 5555) // 2
                    self.device_names[device_id] = f"LDPlayer-{index}" if index > 0 else "LDPlayer"
                    self.connected_devices.add(device_id)
                    logger.info(f"Found device: {device_id}")

        self.devices = devices

        # Disconnect devices that are not active (no running accounts)
        self._cleanup_inactive_connections()

        logger.info(f"Total active devices found: {len(devices)}")
        return devices

    def _test_device_connection(self, device_id: str, name: str = None) -> bool:
        """
        Test device connection với timeout optimization
        """
        try:
            # Try to connect to the device với shorter timeout
            cmd = [self.adb_path, "connect", device_id]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=3)

            if result.returncode == 0:
                # Verify the device is actually responsive với shorter timeout
                test_cmd = [self.adb_path, "-s", device_id, "shell", "echo", "test"]
                test_result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=2)

                if test_result.returncode == 0:
                    return True
                else:
                    # Disconnect if not responsive
                    subprocess.run([self.adb_path, "disconnect", device_id],
                                    capture_output=True, text=True, timeout=2)
            else:
                # Restart ADB server if daemon not running
                if result.returncode == 1 and "daemon not running" in result.stdout.lower():
                    subprocess.run([self.adb_path, "kill-server"], capture_output=True, text=True, timeout=3)
                    subprocess.run([self.adb_path, "start-server"], capture_output=True, text=True, timeout=3)

        except (subprocess.TimeoutExpired, Exception) as e:
            logger.debug(f"Device {device_id} ({name}) not available: {str(e)}")

        return False

    def _cleanup_all_connections(self):
        """
        Cleanup tất cả ADB connections để tránh memory leak
        """
        try:
            # Get list of connected devices
            result = subprocess.run([self.adb_path, "devices"],
                                  capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                for line in lines:
                    if line.strip() and '\t' in line:
                        device_id = line.split('\t')[0]
                        if device_id.startswith('127.0.0.1:'):
                            subprocess.run([self.adb_path, "disconnect", device_id],
                                         capture_output=True, text=True, timeout=2)

            # Kill and restart ADB server để cleanup memory
            subprocess.run([self.adb_path, "kill-server"],
                          capture_output=True, text=True, timeout=3)
            time.sleep(1)
            subprocess.run([self.adb_path, "start-server"],
                          capture_output=True, text=True, timeout=5)

        except Exception as e:
            logger.warning(f"Error cleaning up connections: {str(e)}")

    def _cleanup_inactive_connections(self):
        """
        Disconnect devices that don't have running accounts
        """
        for device_id in self.connected_devices.copy():
            if device_id not in self.active_devices:
                try:
                    subprocess.run([self.adb_path, "disconnect", device_id],
                                 capture_output=True, text=True, timeout=3)
                    self.connected_devices.discard(device_id)
                    logger.info(f"Disconnected inactive device: {device_id}")
                except Exception as e:
                    logger.debug(f"Error disconnecting {device_id}: {str(e)}")

    def connect_device(self, device_id: str) -> bool:
        """
        Connect to a specific device with improved logging
        """
        logger.debug(f"Attempting to connect to device: {device_id}")

        try:
            cmd = [self.adb_path, "connect", device_id]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                logger.debug(f"ADB connect command successful for device: {device_id}")

                # Verify the device is responsive
                test_cmd = [self.adb_path, "-s", device_id, "shell", "echo", "test"]
                test_result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=3)

                if test_result.returncode == 0:
                    self.connected_devices.add(device_id)
                    logger.info(f"Successfully connected and verified device: {device_id}")
                    return True
                else:
                    logger.warning(f"Device {device_id} connected but not responsive")
            else:
                logger.warning(f"ADB connect failed for device {device_id}: {result.stderr}")

            return False
        except Exception as e:
            logger.error(f"Exception while connecting to device {device_id}: {str(e)}")
            return False

    def disconnect_device(self, device_id: str) -> bool:
        """
        Disconnect from a specific device with improved logging
        """
        logger.debug(f"Attempting to disconnect from device: {device_id}")

        try:
            result = subprocess.run([self.adb_path, "disconnect", device_id],
                                  capture_output=True, text=True, timeout=3)
            self.connected_devices.discard(device_id)

            if result.returncode == 0:
                logger.info(f"Successfully disconnected from device: {device_id}")
            else:
                logger.warning(f"ADB disconnect command failed for device {device_id}: {result.stderr}")

            return True
        except Exception as e:
            logger.error(f"Exception while disconnecting from device {device_id}: {str(e)}")
            return False

    def set_device_active(self, device_id: str, active: bool = True):
        """
        Mark device as active (has running account) or inactive with improved logging
        """
        if active:
            logger.debug(f"Setting device {device_id} as active")
            self.active_devices.add(device_id)

            # Ensure device is connected when marked as active
            if device_id not in self.connected_devices:
                logger.info(f"Device {device_id} not connected, attempting connection")
                success = self.connect_device(device_id)
                if not success:
                    logger.error(f"Failed to connect device {device_id} when setting active")
            else:
                logger.debug(f"Device {device_id} already connected")
        else:
            logger.debug(f"Setting device {device_id} as inactive")
            self.active_devices.discard(device_id)

            # Disconnect immediately when marked as inactive
            if device_id in self.connected_devices:
                logger.info(f"Disconnecting inactive device {device_id}")
                self.disconnect_device(device_id)
            else:
                logger.debug(f"Device {device_id} already disconnected")

    def get_device_display_name(self, device_id: str) -> str:
        """
        Get display name for device (LDPlayer name if available, otherwise device_id)
        """
        return self.device_names.get(device_id, device_id)

    def get_devices_with_names(self) -> List[Tuple[str, str]]:
        """
        Get list of devices with their display names
        Returns: List of (device_id, display_name) tuples
        """
        return [(device_id, self.get_device_display_name(device_id)) for device_id in self.devices]

    def tap(self, device_id: str, x: int, y: int) -> bool:
        output = self.execute_command(f"shell input tap {x} {y}", device_id)
        return output is not None


    
    def swipe(self, device_id: str, x1: int, y1: int, x2: int, y2: int, duration: int = 300) -> bool:
        output = self.execute_command(f"shell input swipe {x1} {y1} {x2} {y2} {duration}", device_id)
        return output is not None


    
    def input_text(self, device_id: str, text: str, clear_before: bool = True) -> bool:
        if clear_before:
            for _ in range(20):  # Xoá tối đa 50 ký tự
                self.execute_command("shell input keyevent KEYCODE_DEL", device_id)

        text = text.replace(' ', '%s')
        output = self.execute_command(f"shell input text {text}", device_id)
        self.execute_command("shell input keyevent KEYCODE_ENTER", device_id)
        return output is not None
    
    def screenshot(self, device_id: str) -> Optional[np.ndarray]:
        with self.screenshot_lock:
            try:
                # Sử dụng exec-out để tránh tạo file tạm thời - tối ưu bộ nhớ
                output = subprocess.run(
                    [self.adb_path, "-s", device_id, "exec-out", "screencap", "-p"],
                    capture_output=True,
                    timeout=10  # Thêm timeout để tránh hang
                )

                if output.returncode == 0 and output.stdout:
                    try:
                        # Chuyển đổi trực tiếp từ bytes sang numpy array
                        img = Image.open(io.BytesIO(output.stdout))
                        img_array = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

                        # Giải phóng PIL Image ngay lập tức
                        img.close()
                        del img

                        return img_array
                    except Exception as e:
                        logger.error(f"Image processing error for device {device_id}: {str(e)}")
                        return None
                else:
                    logger.warning(f"Screenshot command failed for device {device_id}: {output.stderr}")
                    return None

            except subprocess.TimeoutExpired:
                logger.error(f"Screenshot timeout for device {device_id}")
                return None
            except Exception as e:
                logger.error(f"Screenshot error for device {device_id}: {str(e)}")
                return None
    
    def get_screen_size(self, device_id: str) -> Tuple[int, int]:
        output = self.execute_command("shell wm size", device_id)
        match = re.search(r'(\d+)x(\d+)', output)
        if match:
            return int(match.group(1)), int(match.group(2))
        return 1920, 1080
    
    def start_app(self, device_id: str, package_name: str, activity_name: str) -> bool:
        """
        Start an app on the device with improved logging
        """
        logger.info(f"Starting app {package_name} on device {device_id}")

        output = self.execute_command(f"shell am start -n {package_name}/{activity_name}", device_id)
        success = output is not None

        if success:
            logger.info(f"Successfully started app {package_name} on device {device_id}")
        else:
            logger.error(f"Failed to start app {package_name} on device {device_id}")

        return success
    
    def stop_app(self, device_id: str, package_name: str) -> bool:
        output = self.execute_command(f"shell am force-stop {package_name}", device_id)
        return output is not None
    
    def is_app_running(self, device_id: str, package_name: str) -> bool:
        """
        Check if an app is running on the device with improved logging
        """
        logger.debug(f"Checking if app {package_name} is running on device {device_id}")

        output = self.execute_command(f"shell pidof {package_name}", device_id)
        is_running = bool(output and output.strip())

        if is_running:
            logger.debug(f"App {package_name} is running on device {device_id}")
        else:
            logger.debug(f"App {package_name} is not running on device {device_id}")

        return is_running