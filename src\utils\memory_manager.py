"""
Memory Management Utilities để tối ưu bộ nhớ cho auto_vltk
"""
import gc
import psutil
import os
import logging
import threading
import time
from typing import Optional
import subprocess

logger = logging.getLogger(__name__)


class MemoryManager:
    """
    Class quản lý bộ nhớ để tránh memory leak
    """
    
    def __init__(self, cleanup_interval: int = 300):  # 5 phút
        self.cleanup_interval = cleanup_interval
        self.cleanup_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        self.memory_threshold = 80  # Phần trăm RAM sử dụng tối đa
        
    def start_monitoring(self):
        """
        Bắt đầu monitor memory và auto cleanup
        """
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            logger.warning("Memory monitoring already running")
            return
            
        self.stop_event.clear()
        self.cleanup_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.cleanup_thread.start()
        logger.info("Memory monitoring started")
        
    def stop_monitoring(self):
        """
        Dừng memory monitoring
        """
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            self.stop_event.set()
            self.cleanup_thread.join(timeout=5)
            logger.info("Memory monitoring stopped")
            
    def _monitoring_loop(self):
        """
        Loop chính để monitor memory
        """
        while not self.stop_event.wait(self.cleanup_interval):
            try:
                memory_percent = self.get_memory_usage()
                logger.debug(f"Current memory usage: {memory_percent:.1f}%")
                
                if memory_percent > self.memory_threshold:
                    logger.warning(f"High memory usage detected: {memory_percent:.1f}%")
                    self.force_cleanup()
                    
                # Cleanup định kỳ
                self.gentle_cleanup()
                
            except Exception as e:
                logger.error(f"Error in memory monitoring: {str(e)}")
                
    def get_memory_usage(self) -> float:
        """
        Lấy phần trăm RAM đang sử dụng
        """
        try:
            return psutil.virtual_memory().percent
        except Exception as e:
            logger.error(f"Error getting memory usage: {str(e)}")
            return 0.0
            
    def get_process_memory(self) -> float:
        """
        Lấy memory usage của process hiện tại (MB)
        """
        try:
            process = psutil.Process(os.getpid())
            return process.memory_info().rss / 1024 / 1024  # Convert to MB
        except Exception as e:
            logger.error(f"Error getting process memory: {str(e)}")
            return 0.0
            
    def gentle_cleanup(self):
        """
        Cleanup nhẹ nhàng - chỉ garbage collection
        """
        try:
            # Force garbage collection
            collected = gc.collect()
            if collected > 0:
                logger.debug(f"Garbage collection freed {collected} objects")
                
        except Exception as e:
            logger.error(f"Error in gentle cleanup: {str(e)}")
            
    def force_cleanup(self):
        """
        Cleanup mạnh mẽ khi memory cao
        """
        try:
            logger.info("Performing force cleanup due to high memory usage")
            
            # Clear template cache
            from .image_utils import clear_template_cache
            clear_template_cache()
            
            # Force garbage collection multiple times
            for _ in range(3):
                collected = gc.collect()
                logger.debug(f"Force GC collected {collected} objects")
                
            # Log memory after cleanup
            memory_after = self.get_memory_usage()
            process_memory = self.get_process_memory()
            logger.info(f"Memory after cleanup: {memory_after:.1f}% system, {process_memory:.1f}MB process")
            
        except Exception as e:
            logger.error(f"Error in force cleanup: {str(e)}")
            
    def cleanup_adb_processes(self):
        """
        Cleanup các ADB process zombie để giải phóng memory
        """
        try:
            # Kill zombie ADB processes
            for proc in psutil.process_iter(['pid', 'name', 'status']):
                try:
                    if proc.info['name'] and 'adb' in proc.info['name'].lower():
                        if proc.info['status'] == psutil.STATUS_ZOMBIE:
                            logger.info(f"Killing zombie ADB process: {proc.info['pid']}")
                            proc.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
        except Exception as e:
            logger.error(f"Error cleaning up ADB processes: {str(e)}")
            
    def restart_adb_server(self, adb_path: str):
        """
        Restart ADB server để cleanup memory
        """
        try:
            logger.info("Restarting ADB server for memory cleanup")
            
            # Kill ADB server
            subprocess.run([adb_path, "kill-server"], 
                          capture_output=True, text=True, timeout=5)
            time.sleep(2)
            
            # Start ADB server
            subprocess.run([adb_path, "start-server"], 
                          capture_output=True, text=True, timeout=10)
            
            logger.info("ADB server restarted successfully")
            
        except Exception as e:
            logger.error(f"Error restarting ADB server: {str(e)}")
            
    def log_memory_stats(self):
        """
        Log chi tiết memory statistics
        """
        try:
            # System memory
            vm = psutil.virtual_memory()
            logger.info(f"System Memory - Total: {vm.total/1024/1024/1024:.1f}GB, "
                       f"Available: {vm.available/1024/1024/1024:.1f}GB, "
                       f"Used: {vm.percent:.1f}%")
            
            # Process memory
            process = psutil.Process(os.getpid())
            mem_info = process.memory_info()
            logger.info(f"Process Memory - RSS: {mem_info.rss/1024/1024:.1f}MB, "
                       f"VMS: {mem_info.vms/1024/1024:.1f}MB")
            
            # Memory by threads
            try:
                thread_count = process.num_threads()
                logger.info(f"Active threads: {thread_count}")
            except:
                pass
                
        except Exception as e:
            logger.error(f"Error logging memory stats: {str(e)}")


# Global memory manager instance
_memory_manager: Optional[MemoryManager] = None


def get_memory_manager() -> MemoryManager:
    """
    Get global memory manager instance
    """
    global _memory_manager
    if _memory_manager is None:
        _memory_manager = MemoryManager()
    return _memory_manager


def start_memory_monitoring():
    """
    Start global memory monitoring
    """
    manager = get_memory_manager()
    manager.start_monitoring()


def stop_memory_monitoring():
    """
    Stop global memory monitoring
    """
    manager = get_memory_manager()
    manager.stop_monitoring()


def force_memory_cleanup():
    """
    Force memory cleanup
    """
    manager = get_memory_manager()
    manager.force_cleanup()


def log_memory_usage():
    """
    Log current memory usage
    """
    manager = get_memory_manager()
    manager.log_memory_stats()
