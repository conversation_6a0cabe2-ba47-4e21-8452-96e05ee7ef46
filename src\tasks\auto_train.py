import time
import logging
from typing import Optional, Tuple
from ..core.account_manager import Account
from ..core.adb_controller import ADBController
from ..utils.image_utils import crop_image, find_image, wait_for_image, wait_for_image_disappear, clear_template_cache
from ..config.config import load_config
from ..utils.memory_manager import get_memory_manager
import gc

logger = logging.getLogger(__name__)


class AutoTrainManager:
    def __init__(self, adb: ADBController, account_manager=None):
        self.adb = adb
        self.account_manager = account_manager
        self.config = load_config()
        
    def start_auto_train(self, account: Account, train_area: str = None,
                        train_map: str = None, train_x: int = None, train_y: int = None) -> bool:
        """
        Bắt đầu auto train cho account với memory optimization
        """
        try:
            # Memory cleanup trước khi bắt đầu
            memory_manager = get_memory_manager()
            memory_manager.gentle_cleanup()

            area = train_area or self.config.get('train_area')
            map_id = train_map or self.config.get('train_map')
            pos_x = train_x or self.config.get('train_x')
            pos_y = train_y or self.config.get('train_y')

            logger.info(f"Starting auto train for {account.name} - Area: {area}, Map: {map_id}, Position: ({pos_x}, {pos_y})")
            # Bước 1: Kiểm tra có đang ở map train không
            if not self._is_in_train_map(account.device_id, area, map_id):
                logger.info(f"Not in train map, navigating to train area for {account.name}")
                screenshot = self.adb.screenshot(account.device_id)
                if screenshot is None:
                    logger.error("Failed to take screenshot")
                    return False

                try:
                    # hanh_trang_pos = find_image(screenshot, "templates/hanh_trang.png")
                    # if hanh_trang_pos:
                    #     self.adb.tap(account.device_id, 993, 33)
                    #     time.sleep(0.5)

                    auto_fight_active = find_image(screenshot, "templates/auto_fight_active.png")
                    if auto_fight_active:
                        self.adb.tap(account.device_id, auto_fight_active[0], auto_fight_active[1])
                        time.sleep(0.5)

                    current_map = self._get_current_map(account.device_id)
                    if not current_map:
                        logger.error("Failed to get current map")
                        return False
                finally:
                    # Giải phóng screenshot ngay sau khi sử dụng
                    del screenshot
                    gc.collect()
                # if current_map == "tuong_duong":
                #     if self.account_manager:
                #         self.account_manager.set_account_task(account.id, "Bán rác")
                #     if not self._ban_rac(account.device_id):
                #         logger.error(f"Lỗi bán rác {account.name}")
                #         return False
                #     if not self._sua_do(account.device_id):
                #         logger.error(f"Lỗi sửa đồ {account.name}")
                #         return False

                if self.account_manager:
                    self.account_manager.set_account_task(account.id, "Tìm thủ khố")
                if not self._move_to_thu_kho(account.device_id, current_map, account.id):
                    logger.error(f"Failed to move to thu kho for {account.name}")
                    return False
                # Click thủ khố
                self.adb.tap(account.device_id, 854, 188)
                time.sleep(0.2)
                # Click rút tiền
                self.adb.tap(account.device_id, 293, 499)
                time.sleep(0.2)
                # Click nhập tiền
                self.adb.tap(account.device_id, 505, 279)
                time.sleep(0.2)
                self.adb.input_text(account.device_id, "2000", False)
                time.sleep(0.5)
                # Click xác nhận
                self.adb.tap(account.device_id, 577, 342)
                time.sleep(0.2)
                # Đóng
                self.adb.tap(account.device_id, 997, 34)
                time.sleep(0.2)

                # Bước 2: Di chuyển đến xa phu
                if self.account_manager:
                    self.account_manager.set_account_task(account.id, "Tìm xa phu")
                if not self._move_to_xa_phu(account.device_id, current_map, account.id):
                    logger.error(f"Failed to move to xa phu for {account.name}")
                    return False
                
                self.adb.tap(account.device_id, 854, 188)
                time.sleep(3)

                # Bước 3: Chọn map luyện công
                if self.account_manager:
                    self.account_manager.set_account_task(account.id, "Chọn bản đồ train")
                if not self._select_train_map(account.device_id, area, map_id):
                    logger.error(f"Failed to select train map {map_id} for {account.name}")
                    return False
            
            self.adb.swipe(account.device_id, 158, 475, 195, 422, 5000)
            time.sleep(1)

            # Click skill
            if self.account_manager:
                self.account_manager.set_account_task(account.id, "Click skill")
            # Click skill
            self.adb.tap(account.device_id, 803, 493)
            time.sleep(0.1)
            # Bước 4: Setup vị trí x y trên map
            if self.account_manager:
                self.account_manager.set_account_task(account.id, "Đến vị trí train")
            if not self._setup_train_position(account.device_id, pos_x, pos_y):
                logger.error(f"Failed to setup train position for {account.name}")
                return False
            
            # Bước 5: Bật auto đánh
            if not self._enable_auto_fight(account.device_id):
                logger.error(f"Failed to enable auto fight for {account.name}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting auto train for {account.name}: {str(e)}")
            return False

    def _ban_rac(self, device_id: str) -> bool:
        """
        Bán rác
        """
        tap_hoa_ho_pos = (203, 203)
        # Đến tạp hóa
        if not self._go_to(device_id, tap_hoa_ho_pos[0], tap_hoa_ho_pos[1]):
            logger.error("Lỗi di chuyển đến tạp hóa")
            return False

        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return False

        if not wait_for_image(self.adb, device_id, f"templates/{tap_hoa_ho_pos[0]}_{tap_hoa_ho_pos[1]}.png", gray_scale=False, timeout=60, threshold=0.95):
            logger.error("Lỗi di chuyển đến tạp hóa")
            return False

        # Click tắt bản đồ
        self.adb.tap(device_id, 960, 52)
        time.sleep(0.5)
        # Click vào tạp hóa
        self.adb.tap(device_id, 853, 178)
        time.sleep(0.5)
        # Click giao dịch
        self.adb.tap(device_id, 348, 359)
        time.sleep(0.5)
        # Lặp qua danh sách item
        pos_list = [(686, 108), (744, 108), (800, 108), (857, 108), (910, 108),
                    (686, 168), (744, 168), (800, 168), (857, 168), (910, 168),
                    (686, 222), (744, 222), (800, 222), (857, 222), (910, 222),
                    (686, 276), (744, 276), (800, 276), (857, 276), (910, 276),
                    (686, 330), (744, 330), (800, 330), (857, 330), (910, 330),
                    (686, 384), (744, 384), (800, 384), (857, 384), (910, 384)]
        for pos in pos_list:
            # Click vào item
            self.adb.tap(device_id, pos[0], pos[1])
            time.sleep(0.5)

            screenshot = self.adb.screenshot(device_id)

            # Cắt ảnh
            screenshot1 = crop_image(screenshot, 370, 1, 284, 570)
        
            # Kiểm tra có phải trang bị không
            tai_phu_pos = find_image(screenshot1, "templates/tai_phu.png", False, 0.7)
            if not tai_phu_pos:
                logger.info("Không phải trang bị, bỏ qua")
                continue

            # Kiểm tra có phải trang bị hoàng kim không
            hoang_kim_pos = find_image(screenshot1, "templates/trang_bi_hoang_kim.png", 0.7)
            if hoang_kim_pos:
                logger.info("Trang bị hoàng kim, bỏ qua")
                continue
            
            # Kiểm tra có phải trang bị cần lọc không
            filter_items = ["hut_noi_luc", "hut_sinh_luc", "khang_tat_ca", "may_man", "phuc_hoi_noi_luc", "phuc_hoi_sinh_luc"]
            is_filter_item = False
            for item in filter_items:
                item_pos = find_image(screenshot1, f"templates/{item}.png", False, 0.7)
                if item_pos:
                    is_filter_item = True
                    # Click bỏ qua
                    self.adb.tap(device_id, 964, 384)
                    time.sleep(0.5)
                    break
            
            if is_filter_item:
                logger.info(f"Trang bị cần lọc, bỏ qua {item}")
                continue

            # Tìm nút bán
            ban_pos = find_image(screenshot, "templates/ban.png")
            if ban_pos:
                self.adb.tap(device_id, ban_pos[0], ban_pos[1])
                time.sleep(0.5)
            else:
                # Click bỏ qua
                self.adb.tap(device_id, 964, 384)
                time.sleep(0.5)
                continue
            
            # Click xác nhận
            self.adb.tap(device_id, 614, 446)
            time.sleep(0.5)
            logger.info("Đã bán")

        screenshot = self.adb.screenshot(device_id)
        screenshot1 = crop_image(screenshot, 651, 74, 291, 347)
        tho_dia_phu_pos = find_image(screenshot1, "templates/tho_dia_phu.png", False)
        if not tho_dia_phu_pos:
            # Click thổ địa phù
            self.adb.tap(device_id, 378, 122)
            time.sleep(0.5)
            # Click mua
            screenshot = self.adb.screenshot(device_id)
            mua_pos = find_image(screenshot, "templates/mua.png")
            if mua_pos:
                self.adb.tap(device_id, mua_pos[0], mua_pos[1])
            time.sleep(0.5)
            # Click xác nhận
            self.adb.tap(device_id, 566, 368)
            time.sleep(0.5)

        # Đóng túi đồ
        self.adb.tap(device_id, 996, 28)
        time.sleep(0.5)
        return True
    
    def _sua_do(self, device_id: str) -> bool:
        """
        Sửa đồ
        """
        tap_hoa_ho_pos = (203, 203)
        # Đến tạp hóa
        if not self._go_to(device_id, tap_hoa_ho_pos[0], tap_hoa_ho_pos[1]):
            logger.error("Lỗi di chuyển đến tạp hóa")
            return False

        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return False

        if not wait_for_image(self.adb, device_id, f"templates/{tap_hoa_ho_pos[0]}_{tap_hoa_ho_pos[1]}.png", gray_scale=False, timeout=60, threshold=0.95):
            logger.error("Lỗi di chuyển đến tạp hóa")
            return False

        # Click tắt bản đồ
        self.adb.tap(device_id, 960, 52)
        time.sleep(0.5)
        # Click vào tạp hóa
        self.adb.tap(device_id, 853, 178)
        time.sleep(0.5)
        # Click sửa đồ
        self.adb.tap(device_id, 678, 361)
        time.sleep(0.5)
        # Click xác nhận
        self.adb.tap(device_id, 343, 361)
        time.sleep(0.5)
        return True

    def _is_in_train_map(self, device_id: str, area: str, map_id: str) -> bool:
        """
        Kiểm tra có đang ở map train không
        """
        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return False
        
        map_template = f"templates/train_map_{area}_{map_id}_loaded.png"
        map_pos = find_image(screenshot, map_template)
        if map_pos:
            logger.info("Already in train map")
            return True
        
        return False
    
    def _move_to_xa_phu(self, device_id: str, current_map: str, account_id: str = None) -> bool:
        """
        Di chuyển đến xa phu với party synchronization
        """
        xa_phu_pos = self.config.get('maps').get(current_map).get('xa_phu')
        if not xa_phu_pos:
            logger.error("Failed to get xa phu position")
            return False
        if not self._go_to(device_id, xa_phu_pos[0], xa_phu_pos[1]):
            logger.error("Failed to go to xa phu")
            return False
        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return False
        if not wait_for_image(self.adb, device_id, f"templates/{xa_phu_pos[0]}_{xa_phu_pos[1]}.png", gray_scale=False, timeout=60, threshold=0.95):
            logger.error("Failed to move to location")
            time.sleep(0.5)
            self.adb.tap(device_id, 959, 68)
            return False
        time.sleep(0.5)
        self.adb.tap(device_id, 959, 68)
        logger.info("Arrived at xa phu")

        # Party synchronization at xa phu
        if account_id and self.account_manager:
            logger.info(f"Account {account_id} reached xa phu, checking party sync...")
            self.account_manager.set_account_task(account_id, "Kiểm tra party sync tại xa phu")

            # Wait for all party members to reach xa phu
            sync_status = self.account_manager.wait_for_party_at_sync_point(
                account_id, "xa_phu", timeout=300  # 5 minutes timeout
            )

            if sync_status == "success":
                logger.info(f"Account {account_id} - Party sync successful, proceeding...")
                self.account_manager.set_account_task(account_id, "Party đã tập hợp tại xa phu")
                # Notify completion to allow cleanup
                self.account_manager.notify_party_sync_complete(account_id, "xa_phu")
            elif sync_status == "skipped_no_party":
                logger.info(f"Account {account_id} - No party, proceeding immediately...")
                self.account_manager.set_account_task(account_id, "Không có party - tiếp tục")
            elif sync_status == "skipped_other_party_waiting":
                logger.info(f"Account {account_id} - Other party waiting, skipping sync...")
                self.account_manager.set_account_task(account_id, "Bỏ qua đợi party - tiếp tục")
            elif sync_status == "timeout":
                logger.warning(f"Account {account_id} - Timeout waiting for party, proceeding anyway...")
                self.account_manager.set_account_task(account_id, "Timeout chờ party - tiếp tục")
                # Still notify completion for cleanup
                self.account_manager.notify_party_sync_complete(account_id, "xa_phu")

        return True
    
    def _move_to_thu_kho(self, device_id: str, current_map: str, account_id: str = None) -> bool:
        """
        Di chuyển đến thủ khố
        """
        thu_kho_pos = self.config.get('maps').get(current_map).get('thu_kho')
        if not thu_kho_pos:
            logger.error("Failed to get thu kho position")
            return False
        if not self._go_to(device_id, thu_kho_pos[0], thu_kho_pos[1]):
            logger.error("Failed to go to thu kho")
            return False
        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return False
        if not wait_for_image(self.adb, device_id, f"templates/{thu_kho_pos[0]}_{thu_kho_pos[1]}.png", gray_scale=False, timeout=60, threshold=0.95):
            logger.error("Failed to move to location")
            time.sleep(0.5)
            self.adb.tap(device_id, 959, 68)
            return False
        time.sleep(0.5)
        self.adb.tap(device_id, 959, 68)
        logger.info("Đã đến thủ khố")

        return True
    
    def _go_to(self, device_id: str, x: int, y: int) -> bool:
        """
        Đi đến vị trí x y
        """
        self.adb.swipe(device_id, 161, 469, 98, 510)
        # Mở minimap
        if not self.open_mini_map(device_id):
            return False

        # Click location icon
        self.adb.tap(device_id, 932, 113)
        time.sleep(1)
        # Click vào ô nhập x
        self.adb.tap(device_id, 719, 174)
        time.sleep(1)
        # Nhập x
        self.adb.input_text(device_id, f"{x}", False)
        time.sleep(1)
        # Click vào ô nhập y
        self.adb.tap(device_id, 835, 174)
        time.sleep(1)
        # Nhập y
        self.adb.input_text(device_id, f"{y}", False)
        time.sleep(1)
        # Click xác nhận
        self.adb.tap(device_id, 910, 183)
        time.sleep(0.5)

        return True
    
    def _get_current_map(self, device_id: str) -> Optional[str]:
        """
        Lấy map hiện tại
        """
        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return None
        
        for map_id, map_data in self.config['maps'].items():
            map_pos = find_image(screenshot, f"templates/{map_id}.png")
            if map_pos:
                logger.info(f"Current map: {map_id}")
                return map_id
        
        return None

    def _select_train_area(self, device_id: str, area: str) -> bool:
        """
        Chọn khu vực luyện công
        """
        # Click vào NPC xa phu
        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return False
        
        xa_phu_npc = find_image(screenshot, "templates/xa_phu_npc.png")
        if xa_phu_npc:
            self.adb.tap(device_id, xa_phu_npc[0], xa_phu_npc[1])
            time.sleep(2)
            
            # Tìm và click vào khu vực train
            area_template = f"templates/train_area_{area}.png"
            if not wait_for_image(self.adb, device_id, area_template, timeout=10):
                logger.error(f"Failed to find train area: {area}")
                return False
            area_pos = find_image(self.adb.screenshot(device_id), area_template)
            if area_pos:
                self.adb.tap(device_id, area_pos[0], area_pos[1])
                time.sleep(2)
                logger.info(f"Selected train area: {area}")
                return True
        
        logger.error(f"Failed to select train area: {area}")
        return False
    
    def _select_train_map(self, device_id: str, area: str, map_id: str) -> bool:
        """
        Chọn map luyện công cụ thể
        """
        self.adb.tap(device_id, 354, 369)
        time.sleep(0.5)
        self.adb.tap(device_id, 350, 440)
        time.sleep(2)
        # map_template = f"templates/train_map_{area}_{map_id}_loaded.png"
        # if wait_for_image(self.adb, device_id, map_template, timeout=20):
        #     logger.info(f"Successfully entered train map: {map_id}")
        return True
            


        # train_pos = find_image(screenshot, "templates/train_area.png")
        # logger.info(f"Train pos: {train_pos}")
        # if train_pos:
        #     self.adb.tap(device_id, train_pos[0], train_pos[1])
        #     time.sleep(1)
        #     screenshot = self.adb.screenshot(device_id)
        #     area_template = f"templates/train_area_{area}.png"
        #     train_area_pos = find_image(self.adb.screenshot(device_id), area_template)
        #     if train_area_pos:
        #         self.adb.tap(device_id, train_area_pos[0], train_area_pos[1])
        #         time.sleep(1)
        #         screenshot = self.adb.screenshot(device_id)
        #         map_template = f"templates/train_map_{area}_{map_id}.png"
        #         map_pos = find_image(self.adb.screenshot(device_id), map_template)
        #         if map_pos:
        #             self.adb.tap(device_id, map_pos[0], map_pos[1])
        #             time.sleep(1)
        #             # Đợi load map
        #             map_template = f"templates/train_map_{area}_{map_id}_loaded.png"
        #             if wait_for_image(self.adb, device_id, map_template, timeout=20):
        #                 logger.info(f"Successfully entered train map: {map_id}")
        #             return True

        logger.error(f"Failed to select train map: {map_id}")
        return False
    
    def _setup_train_position(self, device_id: str, x: int, y: int) -> bool:
        """
        Setup vị trí x y để train
        """
       # Mở minimap
        if not self.open_mini_map(device_id):
            logger.error("Failed to open minimap")
            return False
        
        # Set location
        screenshot = self.adb.screenshot(device_id)
        location_pos = find_image(screenshot, "templates/enter_location.png")
        if location_pos:
            self.adb.tap(device_id, location_pos[0], location_pos[1])
            time.sleep(0.5)
            screenshot = self.adb.screenshot(device_id)

            # Nhập x (sử dụng base coordinates)
            x_pos = find_image(screenshot, "templates/enter_location_x.png")
            if x_pos:
                self.adb.tap(device_id, x_pos[0], x_pos[1])
                time.sleep(0.5)
                self.adb.input_text(device_id, str(x), False)
                time.sleep(0.5)

            # Nhập y (sử dụng base coordinates)
            y_pos = find_image(screenshot, "templates/enter_location_y.png")
            if y_pos:
                self.adb.tap(device_id, y_pos[0], y_pos[1])
                time.sleep(0.5)
                self.adb.input_text(device_id, str(y), False)
                time.sleep(0.5)
            
            # Xác nhận
            confirm_pos = find_image(screenshot, "templates/confirm_location.png")
            if confirm_pos:
                self.adb.tap(device_id, confirm_pos[0], confirm_pos[1])
                time.sleep(1)

            # Đợi di chuyển hoàn thành
            if wait_for_image_disappear(self.adb, device_id, "templates/di_chuyen.png"):
                close_pos = find_image(screenshot, "templates/close_map.png")
                if close_pos:
                    self.adb.tap(device_id, close_pos[0], close_pos[1])
                    time.sleep(1)
            
            logger.info("Location set successfully")
            return True

        return False
    
    def _enable_auto_fight(self, device_id: str) -> bool:
        """
        Bật auto đánh
        """
        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return False
        
        # Tìm và bật auto fight button
        auto_fight_button = find_image(screenshot, "templates/auto_fight_button.png", False)
        if auto_fight_button:
            self.adb.tap(device_id, auto_fight_button[0], auto_fight_button[1])
            time.sleep(1)
            
            # Kiểm tra auto fight đã được bật
            if find_image(self.adb.screenshot(device_id), "templates/auto_fight_active.png", False):
                logger.info("Auto fight enabled successfully")
                return True
        
        logger.error("Failed to enable auto fight")
        return False
    
    def stop_auto_train(self, device_id: str) -> bool:
        """
        Dừng auto train
        """
        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return False
        
        # Tắt auto fight
        auto_fight_button = find_image(screenshot, "templates/auto_fight_button.png", False)
        if auto_fight_button:
            self.adb.tap(device_id, auto_fight_button[0], auto_fight_button[1])
            time.sleep(1)
            logger.info("Auto train stopped")
            return True
        
        return False
    
    def check_train_status(self, device_id: str) -> dict:
        """
        Kiểm tra trạng thái train
        """
        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return {"status": "error", "message": "Cannot take screenshot"}
        
        in_train = True
        maps = ["dai_ly_phu", "tuong_duong", "bien_kinh"]
        for map in maps:
            pos = find_image(screenshot, f"templates/{map}.png")
            if pos:
                in_train = False
                break
        status = {
            "auto_fight_active": bool(find_image(screenshot, "templates/auto_fight_active.png", False )),
            "in_train": in_train
            # "in_combat": bool(find_image(screenshot, "templates/in_combat.png")),
            # "hp_low": bool(find_image(screenshot, "templates/hp_low.png")),
            # "mp_low": bool(find_image(screenshot, "templates/mp_low.png")),
            # "inventory_full": bool(find_image(screenshot, "templates/inventory_full.png"))
        }
        
        return status

    def open_mini_map(self, device_id: str) -> bool:
        """
        Mở mini map
        """ 
        self.adb.tap(device_id, 943, 34)
        time.sleep(0.5)
        return True
    
    def check_tui_do(self, device_id: str) -> bool:
        """
        Kiểm tra túi đồ
        """
        # Click vào túi đồ
        self.adb.tap(device_id, 720, 521)
        time.sleep(0.5)
        screenshot = self.adb.screenshot(device_id)
        if screenshot is None:
            return False
        
        do_trong_pos = find_image(screenshot, "templates/do_trong.png")
        if do_trong_pos:
            self.adb.tap(device_id, 993, 33)
            time.sleep(0.5)
            return True

        tho_dia_phu_pos = find_image(screenshot, "templates/tho_dia_phu.png")
        if tho_dia_phu_pos:
            self.adb.tap(device_id, tho_dia_phu_pos[0], tho_dia_phu_pos[1])
            time.sleep(0.5)
            screenshot = self.adb.screenshot(device_id)
            su_dung_pos = find_image(screenshot, "templates/su_dung.png")
            if su_dung_pos:
                self.adb.tap(device_id, su_dung_pos[0], su_dung_pos[1])
                time.sleep(0.5)
            return True

        return False
    
    def click_skill(self, device_id: str) -> bool:
        """
        Click skill
        """
        self.adb.tap(device_id, 803, 493)
        return True