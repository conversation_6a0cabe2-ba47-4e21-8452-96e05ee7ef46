from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QTableWidget, QTableWidgetItem,
                             QTextEdit, QGroupBox, QLabel, QComboBox, QMessageBox,
                             QHeaderView, QMenu, QAction, QSpinBox, QDialog)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QIcon, QFont, QColor
import logging
from datetime import datetime
from ..core.adb_controller import ADBController
from ..core.account_manager import AccountManager, AccountStatus
from .account_dialog import AccountDialog
from .settings_dialog import SettingsDialog
from ..tasks.auto_task import auto_task

logger = logging.getLogger(__name__)


class MainWindow(QMainWindow):
    update_log_signal = pyqtSignal(str)
    update_task_signal = pyqtSignal(str, str)  # account_id, task
    
    def __init__(self):
        super().__init__()
        self.adb = ADBController()
        self.account_manager = AccountManager(self.adb, max_concurrent_threads=10)  # Default 5 threads
        self.account_manager.register_callback(self.on_account_event)

        self.init_ui()
        self.init_timers()

        # Refresh devices
        self.refresh_devices()

        # Load và hiển thị accounts đã lưu
        self.update_account_table()
        
    def init_ui(self):
        self.setWindowTitle("Auto VLTK - Võ Lâm Truyền Kỳ Tool")
        self.setGeometry(100, 100, 700, 950)  # Even narrower width, taller height
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        
        # Control panel
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)
        
        # Info panel - Device info and statistics (moved to top)
        info_widget = self.create_info_widget()
        main_layout.addWidget(info_widget)

        # Account table
        account_widget = self.create_account_widget()
        main_layout.addWidget(account_widget)

        # Log panel
        log_widget = self.create_log_widget()
        main_layout.addWidget(log_widget)
        
        self.update_log_signal.connect(self.append_log)
        self.update_task_signal.connect(self.update_account_task_cell)
        
    def create_control_panel(self):
        group = QGroupBox("Điều khiển")
        layout = QVBoxLayout()

        # Top row - Device and thread controls
        top_row = QHBoxLayout()

        self.btn_refresh = QPushButton("Làm mới thiết bị")
        self.btn_refresh.clicked.connect(self.refresh_devices)
        top_row.addWidget(self.btn_refresh)

        top_row.addWidget(QLabel("Số thread tối đa:"))
        self.max_threads_spin = QSpinBox()
        self.max_threads_spin.setRange(1, 31)  # ADB limit is 31
        self.max_threads_spin.setValue(10)  # Default to 10 for party support
        self.max_threads_spin.setToolTip("Số lượng account tối đa chạy cùng lúc (giới hạn ADB: 31)")
        self.max_threads_spin.valueChanged.connect(self.update_max_threads)
        top_row.addWidget(self.max_threads_spin)

        top_row.addStretch()

        # Bottom row - Action buttons
        bottom_row = QHBoxLayout()

        self.btn_add_account = QPushButton("Thêm tài khoản")
        self.btn_add_account.clicked.connect(self.add_account)
        bottom_row.addWidget(self.btn_add_account)

        self.btn_start_all = QPushButton("Chạy tất cả")
        self.btn_start_all.clicked.connect(self.start_all_accounts)
        bottom_row.addWidget(self.btn_start_all)

        self.btn_stop_all = QPushButton("Dừng tất cả")
        self.btn_stop_all.clicked.connect(self.stop_all_accounts)
        bottom_row.addWidget(self.btn_stop_all)

        self.btn_settings = QPushButton("Cài đặt")
        self.btn_settings.clicked.connect(self.show_settings)
        bottom_row.addWidget(self.btn_settings)

        bottom_row.addStretch()

        layout.addLayout(top_row)
        layout.addLayout(bottom_row)

        group.setLayout(layout)
        return group
    
    def create_account_widget(self):
        group = QGroupBox("Danh sách tài khoản")
        layout = QVBoxLayout()
        
        self.account_table = QTableWidget()
        self.account_table.setColumnCount(5)
        self.account_table.setHorizontalHeaderLabels([
            "Thiết bị", "Tên", "Trạng thái", "Tác vụ", "Hành động"
        ])

        header = self.account_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Thiết bị
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Tên
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # Tác vụ
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Hành động
        header.setDefaultSectionSize(80)  # Smaller default size

        # Set row height for better display
        self.account_table.verticalHeader().setDefaultSectionSize(50)
        
        self.account_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.account_table.customContextMenuRequested.connect(self.show_account_menu)
        
        layout.addWidget(self.account_table)
        group.setLayout(layout)
        return group
    
    def create_info_widget(self):
        group = QGroupBox("Thông tin")
        layout = QHBoxLayout()  # Changed to horizontal layout

        # Device info section
        device_section = QVBoxLayout()
        device_section.addWidget(QLabel("Thiết bị:"))
        self.device_combo = QComboBox()
        device_section.addWidget(self.device_combo)
        self.device_info_label = QLabel()
        device_section.addWidget(self.device_info_label)

        # Statistics section
        stats_section = QVBoxLayout()
        stats_section.addWidget(QLabel("Thống kê:"))
        self.stats_label = QLabel()
        stats_section.addWidget(self.stats_label)

        # Add sections to horizontal layout
        layout.addLayout(device_section)
        layout.addLayout(stats_section)
        layout.addStretch()

        group.setLayout(layout)

        # Set maximum height to keep it compact
        group.setMaximumHeight(120)

        return group
    
    def create_log_widget(self):
        group = QGroupBox("Nhật ký")
        layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        
        layout.addWidget(self.log_text)
        group.setLayout(layout)
        return group
    
    def init_timers(self):
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.update_display)
        self.refresh_timer.start(1000)

        # Timer để cập nhật account table (để phản ánh thay đổi từ worker threads)
        self.account_timer = QTimer()
        self.account_timer.timeout.connect(self.update_account_table)
        self.account_timer.start(2000)  # Cập nhật mỗi 2 giây

        # self.device_timer = QTimer()
        # self.device_timer.timeout.connect(self.refresh_devices)
        # self.device_timer.start(5000)
    
    def refresh_devices(self):
        devices = self.adb.refresh_devices()
        self.device_combo.clear()

        # Add devices with display names
        for device_id in devices:
            display_name = self.adb.get_device_display_name(device_id)
            self.device_combo.addItem(f"{display_name} ({device_id})", device_id)

        self.update_device_info()
        self.log(f"Tìm thấy {len(devices)} thiết bị")

    def update_max_threads(self):
        """Update maximum concurrent threads"""
        max_threads = self.max_threads_spin.value()
        self.account_manager.set_max_concurrent_threads(max_threads)
        queue_status = self.account_manager.get_queue_status()
        self.log(f"Đã cập nhật số thread tối đa: {max_threads}. Trạng thái: {queue_status['running_accounts']}/{queue_status['max_concurrent']} đang chạy, {queue_status['queued_accounts']} trong hàng đợi")

        # Debug current status
        self.account_manager.debug_status()

    def update_device_info(self):
        device_id = self.device_combo.currentData()  # Get device_id from data
        if device_id:
            display_name = self.adb.get_device_display_name(device_id)
            info_text = f"Tên: {display_name}\nID: {device_id}"
            self.device_info_label.setText(info_text)
        else:
            self.device_info_label.setText("Không có thiết bị nào được chọn")
    
    def add_account(self):
        devices = self.adb.devices
        if not devices:
            QMessageBox.warning(self, "Lỗi", "Không tìm thấy thiết bị nào!")
            return

        dialog = AccountDialog(self.adb, self)
        if dialog.exec_():
            account = dialog.get_account()
            if self.account_manager.add_account(account):
                self.update_account_table()
                self.log(f"Đã thêm tài khoản: {account.name}")
    
    def update_account_table(self):
        self.account_table.setRowCount(len(self.account_manager.accounts))
        
        for row, (acc_id, account) in enumerate(self.account_manager.accounts.items()):
            # Column 0: Thiết bị (Device)
            device_display_name = self.adb.get_device_display_name(account.device_id)
            device_item = QTableWidgetItem(device_display_name)
            # Store account ID in item data for context menu
            device_item.setData(Qt.UserRole, acc_id)
            self.account_table.setItem(row, 0, device_item)

            # Column 1: Tên (Name)
            name_text = f"{account.name}"
            self.account_table.setItem(row, 1, QTableWidgetItem(name_text))

            # Column 2: Trạng thái (Status)
            status_item = QTableWidgetItem(account.status.value)
            if account.status == AccountStatus.RUNNING:
                status_item.setBackground(Qt.green)
            elif account.status == AccountStatus.PENDING:
                status_item.setBackground(Qt.yellow)
            elif account.status == AccountStatus.PAUSED:
                status_item.setBackground(QColor(255, 165, 0))  # Orange
            elif account.status == AccountStatus.ERROR:
                status_item.setBackground(Qt.red)
            elif account.status == AccountStatus.STOPPED:
                status_item.setBackground(Qt.lightGray)
            self.account_table.setItem(row, 2, status_item)

            # Column 3: Tác vụ (Task)
            task_text = self.get_account_current_task(account)
            self.account_table.setItem(row, 3, QTableWidgetItem(task_text))
            
            action_widget = QWidget()
            action_layout = QHBoxLayout(action_widget)
            action_layout.setContentsMargins(0, 0, 0, 0)
            
            if account.status == AccountStatus.IDLE or account.status == AccountStatus.STOPPED:
                btn_start = QPushButton("Chạy")
                btn_start.clicked.connect(lambda checked, aid=acc_id: self.start_account(aid))
                action_layout.addWidget(btn_start)
            elif account.status == AccountStatus.PENDING:
                btn_stop = QPushButton("Dừng")
                btn_stop.clicked.connect(lambda checked, aid=acc_id: self.stop_account(aid))
                action_layout.addWidget(btn_stop)
            elif account.status == AccountStatus.RUNNING:
                btn_pause = QPushButton("Tạm dừng")
                btn_pause.clicked.connect(lambda checked, aid=acc_id: self.pause_account(aid))
                btn_stop = QPushButton("Dừng")
                btn_stop.clicked.connect(lambda checked, aid=acc_id: self.stop_account(aid))
                action_layout.addWidget(btn_pause)
                action_layout.addWidget(btn_stop)

                # Add Skip button if account is waiting for party
                if self.is_account_waiting_for_party(account):
                    btn_skip = QPushButton("Skip")
                    btn_skip.clicked.connect(lambda checked, aid=acc_id: self.skip_party_sync(aid))
                    btn_skip.setStyleSheet("QPushButton { background-color: #FFA500; }")  # Orange color
                    action_layout.addWidget(btn_skip)
            elif account.status == AccountStatus.PAUSED:
                btn_resume = QPushButton("Tiếp tục")
                btn_resume.clicked.connect(lambda checked, aid=acc_id: self.resume_account(aid))
                btn_stop = QPushButton("Dừng")
                btn_stop.clicked.connect(lambda checked, aid=acc_id: self.stop_account(aid))
                action_layout.addWidget(btn_resume)
                action_layout.addWidget(btn_stop)
            
            # Column 4: Hành động (Actions)
            self.account_table.setCellWidget(row, 4, action_widget)

    def get_account_current_task(self, account) -> str:
        """
        Get current task description for account
        """
        if account.status == AccountStatus.IDLE or account.status == AccountStatus.STOPPED:
            return "Chờ"
        elif account.status == AccountStatus.PAUSED:
            return "Tạm dừng"
        elif account.status == AccountStatus.ERROR:
            return "Lỗi"
        elif account.status == AccountStatus.RUNNING:
            # Get current task from account manager or task thread
            task_info = self.account_manager.get_account_task_info(account.id)
            if task_info:
                return task_info
            else:
                return "Đang chạy"
        else:
            return "Không xác định"

    def is_account_waiting_for_party(self, account) -> bool:
        """
        Check if account is currently waiting for party synchronization
        """
        if account.status != AccountStatus.RUNNING:
            return False

        task_info = self.account_manager.get_account_task_info(account.id)
        if not task_info:
            return False

        party_waiting_keywords = [
            "kiểm tra party sync",
            "party sync tại xa phu",
            "đợi party",
            "chờ party",
            "waiting for party",
            "party synchronization",
            "sync tại xa",
            "tập hợp tại xa"
        ]

        return any(keyword in task_info.lower() for keyword in party_waiting_keywords)

    def update_account_task_cell(self, account_id: str, task: str):
        """
        Update only the task cell for a specific account
        """
        # Find the row for this account by checking account ID in item data
        for row in range(self.account_table.rowCount()):
            device_item = self.account_table.item(row, 0)
            if device_item and device_item.data(Qt.UserRole) == account_id:
                # Update task cell (column 3 - Tác vụ)
                self.account_table.setItem(row, 3, QTableWidgetItem(task))
                break

    def show_account_menu(self, position):
        # Get the item at the clicked position
        item = self.account_table.itemAt(position)
        if not item:
            return

        row = item.row()
        if row < 0:
            return

        # Get account ID from the device item's data (column 0)
        device_item = self.account_table.item(row, 0)
        if not device_item:
            return

        acc_id = device_item.data(Qt.UserRole)
        if not acc_id:
            return

        menu = QMenu()

        edit_action = QAction("Sửa", self)
        edit_action.triggered.connect(lambda: self.edit_account(acc_id))
        menu.addAction(edit_action)

        delete_action = QAction("Xóa", self)
        delete_action.triggered.connect(lambda: self.delete_account(acc_id))
        menu.addAction(delete_action)

        menu.exec_(self.account_table.mapToGlobal(position))
    
    def edit_account(self, account_id):
        account = self.account_manager.accounts.get(account_id)
        if not account:
            self.log(f"Lỗi: Không tìm thấy tài khoản {account_id}")
            return

        self.log(f"Mở dialog sửa tài khoản: {account.name}")
        dialog = AccountDialog(self.adb, self, account)
        if dialog.exec_():
            updated_account = dialog.get_account()
            success = self.account_manager.update_account(updated_account)
            if success:
                self.update_account_table()
                self.log(f"Đã cập nhật tài khoản: {updated_account.name}")
            else:
                self.log(f"Lỗi: Không thể cập nhật tài khoản {updated_account.name}")
        else:
            self.log(f"Hủy sửa tài khoản: {account.name}")
    
    def delete_account(self, account_id):
        account = self.account_manager.accounts.get(account_id)
        if not account:
            self.log(f"Lỗi: Không tìm thấy tài khoản {account_id}")
            return

        account_name = account.name
        reply = QMessageBox.question(
            self,
            "Xác nhận",
            f"Bạn có chắc muốn xóa tài khoản '{account_name}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.log(f"Đang xóa tài khoản: {account_name}")
            success = self.account_manager.remove_account(account_id)
            if success:
                self.update_account_table()
                self.log(f"Đã xóa tài khoản: {account_name}")
            else:
                self.log(f"Lỗi: Không thể xóa tài khoản {account_name}")
        else:
            self.log(f"Hủy xóa tài khoản: {account_name}")
    
    def start_account(self, account_id):
        self.account_manager.start_account(account_id, auto_task)
        self.update_account_table()
    
    def stop_account(self, account_id):
        self.account_manager.stop_account(account_id)
        self.update_account_table()
    
    def pause_account(self, account_id):
        self.account_manager.pause_account(account_id)
        self.update_account_table()
    
    def resume_account(self, account_id):
        self.account_manager.resume_account(account_id)
        self.update_account_table()

    def skip_party_sync(self, account_id):
        """Skip party synchronization for an account"""
        if self.account_manager.skip_party_sync(account_id):
            self.update_account_table()
            account = self.account_manager.accounts[account_id]
            self.log(f"Đã bỏ qua đợi party cho tài khoản: {account.name}")
        else:
            self.log(f"Không thể bỏ qua party sync cho tài khoản: {account_id}")
    
    def start_all_accounts(self):
        from ..tasks.auto_task import auto_task
        started_count = self.account_manager.start_all_accounts(auto_task)
        self.update_account_table()
        queue_status = self.account_manager.get_queue_status()
        self.log(f"Đã bắt đầu/xếp hàng {started_count} tài khoản. Trạng thái: {queue_status['running_accounts']}/{queue_status['max_concurrent']} đang chạy, {queue_status['queued_accounts']} trong hàng đợi")

    def stop_all_accounts(self):
        stopped_count = self.account_manager.stop_all_accounts()
        self.update_account_table()
        self.log(f"Đã dừng {stopped_count} tài khoản và xóa hàng đợi")
    
    def show_settings(self):
        dialog = SettingsDialog(self)
        dialog.exec_()
    
    def on_account_event(self, event, data):
        if event == "task_updated":
            # Use signal to ensure GUI update runs on main thread
            self.update_task_signal.emit(data["account_id"], data["task"])
        # Note: Other events from worker threads are handled by polling timer
        # Only handle events that come from main thread operations
    
    def update_display(self):
        self.update_statistics()
    
    def update_statistics(self):
        total = len(self.account_manager.accounts)
        running = sum(1 for acc in self.account_manager.accounts.values()
                     if acc.status == AccountStatus.RUNNING)
        pending = sum(1 for acc in self.account_manager.accounts.values()
                     if acc.status == AccountStatus.PENDING)
        paused = sum(1 for acc in self.account_manager.accounts.values()
                    if acc.status == AccountStatus.PAUSED)
        error = sum(1 for acc in self.account_manager.accounts.values()
                   if acc.status == AccountStatus.ERROR)

        # Get queue status
        queue_status = self.account_manager.get_queue_status()

        stats_text = f"""
        Tổng số tài khoản: {total}
        Đang chạy: {running}/{queue_status['max_concurrent']}
        Chờ xử lý: {pending}
        Tạm dừng: {paused}
        Lỗi: {error}
        """
        self.stats_label.setText(stats_text)
    
    def log(self, message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.update_log_signal.emit(f"[{timestamp}] {message}")
    
    def append_log(self, message):
        self.log_text.append(message)
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())